# 快速开始指南

## 🚀 三步运行

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 运行程序
```bash
python main.py
```

### 3. 选择模式
- **选项1**: 基础优化 (几秒钟) - 快速获得可行解
- **选项2**: NSGA-II优化 (10-30分钟) - 完整的多目标优化

## 📁 文件说明

- `main.py` - 主程序
- `代码数据.xlsx` - 水资源数据（必需）
- `README.md` - 详细说明文档
- `requirements.txt` - 依赖包列表

## 📊 输出结果

- `analysis_result.xlsx` - 详细配置方案
- `pareto_front.png` - 帕累托前沿图（仅NSGA-II模式）

## ⚠️ 注意事项

1. 确保 `代码数据.xlsx` 文件存在
2. NSGA-II优化需要10-30分钟，请耐心等待
3. 建议先运行基础优化验证数据正确性

## 🔧 如有问题

查看 `README.md` 中的常见问题解答部分。
