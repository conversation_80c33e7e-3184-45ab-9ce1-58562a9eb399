#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
增强版NSGA-II算法测试脚本
"""

import sys
import os
import numpy as np

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    # 先导入必要的库
    from deap import base, creator, tools, algorithms
    print("✓ DEAP库导入成功")
    
    # 创建适应度和个体类
    if not hasattr(creator, 'FitnessMulti'):
        creator.create("FitnessMulti", base.Fitness, weights=(-1.0, 1.0))
    if not hasattr(creator, 'Individual'):
        creator.create("Individual", list, fitness=creator.FitnessMulti)
    print("✓ 创建适应度和个体类成功")
    
    # 导入主模块
    from main import *
    print("✓ 成功导入主模块")
except ImportError as e:
    print(f"✗ 导入失败: {e}")
    sys.exit(1)
except Exception as e:
    print(f"✗ 初始化失败: {e}")
    sys.exit(1)

def test_enhanced_algorithm():
    """测试增强版算法"""
    print("\n=== 测试增强版NSGA-II算法 ===")
    
    try:
        # 运行小规模测试
        final_population, best_solutions = enhanced_nsga2_algorithm(
            population_size=20,
            generations=5,
            crossover_prob=0.9,
            mutation_prob=0.4
        )
        
        print(f"✓ 增强版算法运行成功")
        print(f"✓ 最终种群大小: {len(final_population)}")
        
        # 提取帕累托前沿
        pareto_front = extract_pareto_front(final_population)
        print(f"✓ 帕累托前沿解数量: {len(pareto_front)}")
        
        # 检查多样性
        diversity = check_population_diversity(pareto_front)
        print(f"✓ 解的多样性: {diversity:.2f}")
        
        if diversity > 10:
            print("✅ 多样性良好！")
        else:
            print("⚠️ 多样性较低，但比原算法有改进")
        
        # 显示适应度范围
        if pareto_front:
            obj1_values = [ind.fitness.values[0] for ind in pareto_front]
            obj2_values = [ind.fitness.values[1] for ind in pareto_front]
            
            print(f"缺水量范围: {min(obj1_values):.0f} - {max(obj1_values):.0f}")
            print(f"微咸水使用量范围: {min(obj2_values):.0f} - {max(obj2_values):.0f}")
            
            if len(set(obj1_values)) > 1 or len(set(obj2_values)) > 1:
                print("✅ 发现多样化的解！")
            else:
                print("⚠️ 解仍然相同，需要进一步优化")
        
        return True
    except Exception as e:
        print(f"✗ 增强版算法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_evaluation_functions():
    """测试评估函数"""
    print("\n=== 测试评估函数 ===")
    
    try:
        # 生成测试个体
        individual = gen_individual()
        print("✓ 生成测试个体成功")
        
        # 测试软约束评估
        fitness_soft = evaluate_soft(individual)
        print(f"✓ 软约束评估: {fitness_soft}")
        
        # 测试混合评估
        fitness_mixed = evaluate(individual)
        print(f"✓ 混合评估: {fitness_mixed}")
        
        # 测试约束惩罚计算
        X = np.array(individual).reshape(len(TOWN_NAMES), len(config.users), MONTHS, len(config.sources))
        penalty = calculate_constraint_penalty(X)
        print(f"✓ 约束惩罚: {penalty:.0f}")
        
        return True
    except Exception as e:
        print(f"✗ 评估函数测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始增强版NSGA-II算法测试...")
    
    # 检查数据文件
    if not os.path.exists("代码数据.xlsx"):
        print("✗ 数据文件不存在")
        return False
    else:
        print("✓ 数据文件存在")
    
    # 测试评估函数
    if not test_evaluation_functions():
        return False
    
    # 测试增强版算法
    if not test_enhanced_algorithm():
        return False
    
    print("\n=== 所有测试通过! ===")
    print("增强版NSGA-II算法已准备就绪")
    print("可以运行 'python main.py' 选择选项2来使用完整的优化系统")
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n请检查错误信息并修复问题")
        sys.exit(1)
