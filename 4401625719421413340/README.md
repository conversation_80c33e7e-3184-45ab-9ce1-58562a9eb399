# 水资源配置优化系统 - NSGA-II多目标优化

## 项目简介

本项目是一个基于NSGA-II算法的水资源配置多目标优化系统，用于解决八个乡镇的水资源分配问题。系统考虑多种水源（地表水、引黄水、引江水、地下水、微咸水、再生水）和多种用户需求（城镇生活、农村生活、工业三产、生态、农业）。

## 主要功能

### 1. 基础优化方法
- **算法类型**: 修复算法（Repair Algorithm）
- **运行时间**: 几秒钟
- **结果**: 1个可行解
- **特点**: 快速获得满足所有约束条件的水资源配置方案

### 2. NSGA-II多目标优化
- **算法类型**: NSGA-II遗传算法
- **运行时间**: 10-30分钟
- **结果**: 多个帕累托最优解（10-30个）
- **优化目标**:
  - 目标1: 最小化总缺水量
  - 目标2: 最大化微咸水使用量
- **特点**: 生成完整的帕累托前沿解集，提供多个优化方案供决策选择

## 文件结构

```
4401625719421413340/
├── main.py           # 主程序文件
├── 代码数据.xlsx     # 数据文件
└── README.md         # 使用说明
```

## 数据文件说明

Excel文件包含以下工作表：
- **农业需水**: 各乡镇农业用水需求（按月）
- **固定需水**: 城镇生活、农村生活、工业三产、生态用水需求
- **地下水**: 各乡镇地下水可用量限制
- **微咸水**: 2-3g/L和3-5g/L微咸水可用量
- **地表水**: 各乡镇地表水可用量

## 环境要求

### Python版本
- Python 3.7 或更高版本

### 依赖包安装
```bash
pip install -r requirements.txt
```

或手动安装：
```bash
pip install numpy pandas matplotlib deap openpyxl
```

## 使用方法

### 1. 运行程序
```bash
python main.py
```

### 2. 选择优化方法
程序启动后会显示菜单：
- **选项1**: 基础优化方法 - 快速获得可行解
- **选项2**: NSGA-II多目标优化 - 完整的帕累托前沿分析

输入对应数字选择运行模式。

### 3. 推荐使用流程
1. 首先运行基础优化方法，验证数据和程序正常
2. 然后运行NSGA-II多目标优化，获得完整的优化结果

## 输出结果

### 基础优化输出
- `analysis_result.xlsx` - 详细的水资源配置方案
- 包含各乡镇、各用户、各月份的供水配置
- 水源利用情况和缺水量统计

### NSGA-II优化输出
- `analysis_result.xlsx` - 最优解的详细配置方案
- `pareto_front.png` - 帕累托前沿图（自动配置中文字体）
- 控制台显示多个优化解的特征分析
- 帕累托前沿解集的统计信息

## 算法参数

### NSGA-II参数设置
- **种群大小**: 50个个体
- **进化代数**: 30代
- **交叉概率**: 0.8
- **变异概率**: 0.2
- **总评估次数**: 约3000次
- **预计运行时间**: 10-30分钟

可在 `main.py` 中的 `run_nsga2_optimization()` 函数内调整这些参数。

## 约束条件

系统考虑以下约束：
1. **水源供应限制**: 各类水源的月度和年度供应上限
2. **需水量平衡**: 满足各用户的基本需水需求
3. **微咸水配比**: 微咸水使用需要一定比例的淡水稀释
4. **时间约束**: 引黄水仅在特定月份可用
5. **空间约束**: 考虑乡镇间的水源调配关系

## 技术特点

### 多目标优化
- 使用NSGA-II算法处理多目标冲突
- 生成帕累托最优解集，提供多种平衡方案
- 同时优化缺水量最小化和微咸水使用量最大化

### 智能修复机制
- 自动修复不可行解，确保所有约束条件得到满足
- 按优先级分配水资源（微咸水→再生水→引江水→引黄水→地表水→地下水）
- 考虑时间和空间约束的复杂水资源调配

### 中文字体支持
- 自动检测操作系统并配置合适的中文字体
- 支持macOS、Windows、Linux系统
- 图表中文显示正常，无方块问题

### 详细分析报告
- Excel格式的详细配置方案
- 按乡镇、用户、月份的精细化分析
- 水源利用率和缺水量统计

## 常见问题

### Q1: 程序运行时间过长怎么办？
**解决方案**：
- NSGA-II算法正常运行时间为10-30分钟
- 可以在代码中减小种群大小和进化代数
- 先运行基础优化验证数据正确性

### Q2: 图片中文显示为方块
**解决方案**：
- 程序已自动配置中文字体
- 如仍有问题，请安装系统中文字体包
- 更新matplotlib到最新版本

### Q3: Excel文件读取错误
**解决方案**：
- 确保 `代码数据.xlsx` 文件在程序目录下
- 检查Excel文件是否包含所有必要的工作表
- 确保数据格式正确（数值型数据）

### Q4: 内存不足
**解决方案**：
- 减小NSGA-II的种群大小（如改为30）
- 关闭其他程序释放内存
- 使用性能更好的计算机

## 注意事项

1. **数据文件必须存在**：程序严格依赖真实Excel数据，不使用模拟数据
2. **运行时间**：NSGA-II优化需要10-30分钟，请耐心等待
3. **结果文件**：所有输出文件保存在程序运行目录下
4. **推荐流程**：先运行基础优化验证，再运行NSGA-II获得完整结果

## 系统要求

- **操作系统**: Windows/macOS/Linux
- **Python版本**: 3.7+
- **内存**: 建议4GB以上
- **存储空间**: 至少100MB可用空间
- **运行时间**: 基础优化几秒钟，NSGA-II优化10-30分钟
