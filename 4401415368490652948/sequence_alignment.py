#!/usr/bin/env python3
"""
Dynamic Programming Sequence Alignment
Implements both global and local sequence alignment algorithms
"""

import sys
import argparse

class SequenceAligner:
    def __init__(self):
        self.seq_a = ""
        self.seq_b = ""
        self.is_local = False
        self.gap_penalty_a = 0
        self.gap_penalty_b = 0
        self.alphabet = ""
        self.score_matrix = {}
        self.dp_matrix = []
        self.traceback_matrix = []
        
    def parse_input_file(self, filename):
        """Parse the input file according to project specifications"""
        with open(filename, 'r') as f:
            lines = [line.strip() for line in f.readlines() if line.strip()]

        line_idx = 0

        # Parse sequence A
        self.seq_a = lines[line_idx]
        line_idx += 1

        # Parse sequence B
        self.seq_b = lines[line_idx]
        line_idx += 1

        # Parse alignment type (0 = global, 1 = local)
        self.is_local = int(lines[line_idx]) == 1
        line_idx += 1

        # Parse gap penalties (4 values: open_A, extend_A, open_B, extend_B)
        gap_penalties = lines[line_idx].split()
        self.gap_penalty_a = -float(gap_penalties[0])  # Gap penalties should be negative
        self.gap_penalty_b = -float(gap_penalties[2])  # Gap penalties should be negative
        line_idx += 1

        # Parse alphabet size for A
        alphabet_size_a = int(lines[line_idx])
        line_idx += 1

        # Parse alphabet for A
        alphabet_a = lines[line_idx]
        line_idx += 1

        # Parse alphabet size for B
        alphabet_size_b = int(lines[line_idx])
        line_idx += 1

        # Parse alphabet for B
        alphabet_b = lines[line_idx]
        line_idx += 1

        # Parse score matrix entries
        self.score_matrix = {}
        for i in range(alphabet_size_a * alphabet_size_b):
            parts = lines[line_idx + i].split()
            row_idx = int(parts[0]) - 1  # Convert to 0-based
            col_idx = int(parts[1]) - 1  # Convert to 0-based
            char_a = parts[2]
            char_b = parts[3]
            score = float(parts[4])
            self.score_matrix[(char_a, char_b)] = score
    
    def initialize_matrices(self):
        """Initialize DP and traceback matrices"""
        rows = len(self.seq_a) + 1
        cols = len(self.seq_b) + 1
        
        # Initialize DP matrix
        self.dp_matrix = [[0.0 for _ in range(cols)] for _ in range(rows)]
        
        # Initialize traceback matrix
        # 0: diagonal, 1: up (gap in B), 2: left (gap in A)
        self.traceback_matrix = [[0 for _ in range(cols)] for _ in range(rows)]
        
        if not self.is_local:
            # Global alignment initialization
            for i in range(1, rows):
                self.dp_matrix[i][0] = self.dp_matrix[i-1][0] + self.gap_penalty_a
                self.traceback_matrix[i][0] = 1
            
            for j in range(1, cols):
                self.dp_matrix[0][j] = self.dp_matrix[0][j-1] + self.gap_penalty_b
                self.traceback_matrix[0][j] = 2
        # Local alignment starts with all zeros (already initialized)
    
    def fill_matrices(self):
        """Fill the DP matrix using dynamic programming"""
        rows = len(self.seq_a) + 1
        cols = len(self.seq_b) + 1
        
        for i in range(1, rows):
            for j in range(1, cols):
                # Match/mismatch score
                match_score = self.score_matrix.get((self.seq_a[i-1], self.seq_b[j-1]), 0)
                diagonal = self.dp_matrix[i-1][j-1] + match_score
                
                # Gap scores
                up = self.dp_matrix[i-1][j] + self.gap_penalty_a
                left = self.dp_matrix[i][j-1] + self.gap_penalty_b
                
                # Choose best score
                if diagonal >= up and diagonal >= left:
                    self.dp_matrix[i][j] = diagonal
                    self.traceback_matrix[i][j] = 0  # diagonal
                elif up >= left:
                    self.dp_matrix[i][j] = up
                    self.traceback_matrix[i][j] = 1  # up
                else:
                    self.dp_matrix[i][j] = left
                    self.traceback_matrix[i][j] = 2  # left
                
                # For local alignment, don't allow negative scores
                if self.is_local and self.dp_matrix[i][j] < 0:
                    self.dp_matrix[i][j] = 0.0
    
    def find_best_score_position(self):
        """Find the position of the best score for traceback"""
        rows = len(self.seq_a) + 1
        cols = len(self.seq_b) + 1
        
        if self.is_local:
            # Local alignment: find maximum score anywhere in matrix
            max_score = 0.0
            max_i, max_j = 0, 0
            
            for i in range(rows):
                for j in range(cols):
                    if self.dp_matrix[i][j] > max_score:
                        max_score = self.dp_matrix[i][j]
                        max_i, max_j = i, j
            
            return max_i, max_j, max_score
        else:
            # Global alignment: the optimal score is at bottom-right corner
            max_i, max_j = rows - 1, cols - 1
            max_score = self.dp_matrix[max_i][max_j]

            return max_i, max_j, max_score
    
    def find_all_optimal_alignments(self, start_i, start_j):
        """Find all optimal alignments using recursive traceback"""
        alignments = []

        def traceback_recursive(i, j, aligned_a, aligned_b):
            if i == 0 and j == 0:
                # Reached the beginning, add this alignment
                alignments.append((''.join(reversed(aligned_a)), ''.join(reversed(aligned_b))))
                return

            if self.is_local and self.dp_matrix[i][j] == 0:
                alignments.append((''.join(reversed(aligned_a)), ''.join(reversed(aligned_b))))
                return

            current_score = self.dp_matrix[i][j]

            # Check all possible moves that could have led to current score
            moves_found = []

            # Diagonal move (match/mismatch)
            if i > 0 and j > 0:
                match_score = self.score_matrix.get((self.seq_a[i-1], self.seq_b[j-1]), 0)
                if abs(self.dp_matrix[i-1][j-1] + match_score - current_score) < 1e-10:
                    moves_found.append(('diagonal', i-1, j-1, self.seq_a[i-1], self.seq_b[j-1]))

            # Up move (gap in sequence B)
            if i > 0:
                if abs(self.dp_matrix[i-1][j] + self.gap_penalty_a - current_score) < 1e-10:
                    moves_found.append(('up', i-1, j, self.seq_a[i-1], '_'))

            # Left move (gap in sequence A)
            if j > 0:
                if abs(self.dp_matrix[i][j-1] + self.gap_penalty_b - current_score) < 1e-10:
                    moves_found.append(('left', i, j-1, '_', self.seq_b[j-1]))

            # Recursively explore all valid moves
            for _, new_i, new_j, char_a, char_b in moves_found:
                new_aligned_a = aligned_a + [char_a]
                new_aligned_b = aligned_b + [char_b]
                traceback_recursive(new_i, new_j, new_aligned_a, new_aligned_b)

        traceback_recursive(start_i, start_j, [], [])
        return alignments

    def traceback(self, start_i, start_j):
        """Perform traceback to get one alignment (for backward compatibility)"""
        alignments = self.find_all_optimal_alignments(start_i, start_j)
        if alignments:
            return alignments[0]
        return "", ""
    
    def align(self):
        """Perform the complete alignment process"""
        self.initialize_matrices()
        self.fill_matrices()

        best_i, best_j, best_score = self.find_best_score_position()
        all_alignments = self.find_all_optimal_alignments(best_i, best_j)

        return best_score, all_alignments
    
    def write_output(self, filename, score, all_alignments):
        """Write all alignment results to output file"""
        with open(filename, 'w') as f:
            # Format score to avoid floating point precision issues
            if abs(score - round(score)) < 1e-10:
                f.write(f"{round(score):.1f}\n")
            else:
                f.write(f"{score:.1f}\n")

            # Write all alignments
            for i, (aligned_a, aligned_b) in enumerate(all_alignments):
                f.write(f"\n")  # Empty line before alignment
                f.write(f"{aligned_a}\n")
                f.write(f"{aligned_b}\n")
                if i < len(all_alignments) - 1:
                    f.write(f"\n")  # Empty line between alignments

            f.write(f"\n")  # Final empty line

def main():
    if len(sys.argv) != 3:
        print("Usage: python sequence_alignment.py <input_file> <output_file>")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2]
    
    aligner = SequenceAligner()
    aligner.parse_input_file(input_file)

    score, all_alignments = aligner.align()
    aligner.write_output(output_file, score, all_alignments)

    print(f"Alignment completed. Results written to {output_file}")
    if abs(score - round(score)) < 1e-10:
        print(f"Score: {round(score):.1f}")
    else:
        print(f"Score: {score:.1f}")

    print(f"Found {len(all_alignments)} optimal alignment(s):")
    for i, (aligned_a, aligned_b) in enumerate(all_alignments):
        print(f"Alignment {i+1}:")
        print(f"  Sequence A: {aligned_a}")
        print(f"  Sequence B: {aligned_b}")

if __name__ == "__main__":
    main()
