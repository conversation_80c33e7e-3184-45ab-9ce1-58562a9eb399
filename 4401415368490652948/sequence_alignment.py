#!/usr/bin/env python3
"""
Dynamic Programming Sequence Alignment
Implements both global and local sequence alignment algorithms
"""

import sys
import argparse

class SequenceAligner:
    def __init__(self):
        self.seq_a = ""
        self.seq_b = ""
        self.is_local = False
        self.gap_penalty_a = 0
        self.gap_penalty_b = 0
        self.alphabet = ""
        self.score_matrix = {}
        self.dp_matrix = []
        self.traceback_matrix = []
        
    def parse_input_file(self, filename):
        """Parse the input file according to project specifications"""
        with open(filename, 'r') as f:
            lines = [line.strip() for line in f.readlines() if line.strip()]
        
        line_idx = 0
        
        # Parse sequence A
        self.seq_a = lines[line_idx]
        line_idx += 1
        
        # Parse sequence B
        self.seq_b = lines[line_idx]
        line_idx += 1
        
        # Parse alignment type (0 = global, 1 = local)
        self.is_local = int(lines[line_idx]) == 1
        line_idx += 1
        
        # Parse gap penalties
        gap_penalties = lines[line_idx].split()
        self.gap_penalty_a = float(gap_penalties[0])
        self.gap_penalty_b = float(gap_penalties[1])
        line_idx += 1
        
        # Parse alphabet
        self.alphabet = lines[line_idx]
        line_idx += 1
        
        # Parse score matrix
        self.score_matrix = {}
        for i, char_a in enumerate(self.alphabet):
            scores = list(map(float, lines[line_idx + i].split()))
            for j, char_b in enumerate(self.alphabet):
                self.score_matrix[(char_a, char_b)] = scores[j]
    
    def initialize_matrices(self):
        """Initialize DP and traceback matrices"""
        rows = len(self.seq_a) + 1
        cols = len(self.seq_b) + 1
        
        # Initialize DP matrix
        self.dp_matrix = [[0.0 for _ in range(cols)] for _ in range(rows)]
        
        # Initialize traceback matrix
        # 0: diagonal, 1: up (gap in B), 2: left (gap in A)
        self.traceback_matrix = [[0 for _ in range(cols)] for _ in range(rows)]
        
        if not self.is_local:
            # Global alignment initialization
            for i in range(1, rows):
                self.dp_matrix[i][0] = self.dp_matrix[i-1][0] + self.gap_penalty_a
                self.traceback_matrix[i][0] = 1
            
            for j in range(1, cols):
                self.dp_matrix[0][j] = self.dp_matrix[0][j-1] + self.gap_penalty_b
                self.traceback_matrix[0][j] = 2
        # Local alignment starts with all zeros (already initialized)
    
    def fill_matrices(self):
        """Fill the DP matrix using dynamic programming"""
        rows = len(self.seq_a) + 1
        cols = len(self.seq_b) + 1
        
        for i in range(1, rows):
            for j in range(1, cols):
                # Match/mismatch score
                match_score = self.score_matrix.get((self.seq_a[i-1], self.seq_b[j-1]), 0)
                diagonal = self.dp_matrix[i-1][j-1] + match_score
                
                # Gap scores
                up = self.dp_matrix[i-1][j] + self.gap_penalty_a
                left = self.dp_matrix[i][j-1] + self.gap_penalty_b
                
                # Choose best score
                if diagonal >= up and diagonal >= left:
                    self.dp_matrix[i][j] = diagonal
                    self.traceback_matrix[i][j] = 0  # diagonal
                elif up >= left:
                    self.dp_matrix[i][j] = up
                    self.traceback_matrix[i][j] = 1  # up
                else:
                    self.dp_matrix[i][j] = left
                    self.traceback_matrix[i][j] = 2  # left
                
                # For local alignment, don't allow negative scores
                if self.is_local and self.dp_matrix[i][j] < 0:
                    self.dp_matrix[i][j] = 0.0
    
    def find_best_score_position(self):
        """Find the position of the best score for traceback"""
        rows = len(self.seq_a) + 1
        cols = len(self.seq_b) + 1
        
        if self.is_local:
            # Local alignment: find maximum score anywhere in matrix
            max_score = 0.0
            max_i, max_j = 0, 0
            
            for i in range(rows):
                for j in range(cols):
                    if self.dp_matrix[i][j] > max_score:
                        max_score = self.dp_matrix[i][j]
                        max_i, max_j = i, j
            
            return max_i, max_j, max_score
        else:
            # Global alignment: check last row and column
            max_score = float('-inf')
            max_i, max_j = rows - 1, cols - 1
            
            # Check last row
            for j in range(cols):
                if self.dp_matrix[rows-1][j] > max_score:
                    max_score = self.dp_matrix[rows-1][j]
                    max_i, max_j = rows - 1, j
            
            # Check last column
            for i in range(rows):
                if self.dp_matrix[i][cols-1] > max_score:
                    max_score = self.dp_matrix[i][cols-1]
                    max_i, max_j = i, cols - 1
            
            return max_i, max_j, max_score
    
    def traceback(self, start_i, start_j):
        """Perform traceback to get the alignment"""
        aligned_a = []
        aligned_b = []
        
        i, j = start_i, start_j
        
        while i > 0 or j > 0:
            if self.is_local and self.dp_matrix[i][j] == 0:
                break
                
            if i > 0 and j > 0 and self.traceback_matrix[i][j] == 0:
                # Diagonal: match/mismatch
                aligned_a.append(self.seq_a[i-1])
                aligned_b.append(self.seq_b[j-1])
                i -= 1
                j -= 1
            elif i > 0 and self.traceback_matrix[i][j] == 1:
                # Up: gap in sequence B
                aligned_a.append(self.seq_a[i-1])
                aligned_b.append('-')
                i -= 1
            elif j > 0:
                # Left: gap in sequence A
                aligned_a.append('-')
                aligned_b.append(self.seq_b[j-1])
                j -= 1
            else:
                break
        
        # Reverse the alignments (built backwards)
        aligned_a.reverse()
        aligned_b.reverse()
        
        return ''.join(aligned_a), ''.join(aligned_b)
    
    def align(self):
        """Perform the complete alignment process"""
        self.initialize_matrices()
        self.fill_matrices()
        
        best_i, best_j, best_score = self.find_best_score_position()
        aligned_a, aligned_b = self.traceback(best_i, best_j)
        
        return best_score, aligned_a, aligned_b
    
    def write_output(self, filename, score, aligned_a, aligned_b):
        """Write the alignment results to output file"""
        with open(filename, 'w') as f:
            f.write(f"{score}\n")
            f.write(f"{aligned_a}\n")
            f.write(f"{aligned_b}\n")

def main():
    if len(sys.argv) != 3:
        print("Usage: python sequence_alignment.py <input_file> <output_file>")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2]
    
    aligner = SequenceAligner()
    aligner.parse_input_file(input_file)
    
    score, aligned_a, aligned_b = aligner.align()
    aligner.write_output(output_file, score, aligned_a, aligned_b)
    
    print(f"Alignment completed. Results written to {output_file}")
    print(f"Score: {score}")
    print(f"Sequence A: {aligned_a}")
    print(f"Sequence B: {aligned_b}")

if __name__ == "__main__":
    main()
