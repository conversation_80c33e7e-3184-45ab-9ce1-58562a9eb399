BIOINFORMATICS PROGRAMMING PROJECTS
===================================

Name: AI Assistant
Language: Python 3

This directory contains implementations for two bioinformatics programming projects:

PROJECT 1: DYNAMIC PROGRAMMING SEQUENCE ALIGNMENT
=================================================

Files:
- sequence_alignment.py: Main implementation
- README_project1.txt: Detailed documentation
- test_input.txt, test_local_input.txt: Test cases

Usage: python sequence_alignment.py <input_file> <output_file>

Features:
- Supports both global and local sequence alignment
- Dynamic programming algorithm with O(mn) time complexity
- Handles custom scoring matrices and gap penalties
- Produces optimal alignments with traceback

PROJECT 2: K-MEANS CLUSTERING
=============================

Files:
- kmeans.py: Main implementation  
- README_project2.txt: Detailed documentation
- test_data.dat, test_centroids.txt: Test cases

Usage: python kmeans.py <k> <data_file> <max_iterations> [centroids_file]

Features:
- K-means clustering for microarray gene expression data
- Supports random or file-based centroid initialization
- Euclidean distance metric
- Convergence detection and iteration limits
- Output in required format (gene_number<tab>cluster_number)

TESTING:
========

Both projects have been tested with sample data:

Project 1:
- Global alignment: test_input.txt -> test_output.txt
- Local alignment: test_local_input.txt -> test_local_output.txt

Project 2:
- With initial centroids: kmeans.py 3 test_data.dat 10 test_centroids.txt
- Random initialization: kmeans.py 3 test_data.dat 10

All tests produce expected results and demonstrate correct algorithm implementation.

ALGORITHM SUMMARIES:
===================

Sequence Alignment:
1. Initialize DP matrix based on alignment type
2. Fill matrix using recurrence relation
3. Find optimal score position
4. Traceback to reconstruct alignment

K-means Clustering:
1. Initialize k centroids (random or from file)
2. Assign points to nearest centroids
3. Update centroids as cluster means
4. Repeat until convergence or max iterations
5. Output cluster assignments

Both algorithms are implemented with proper error handling, documentation, and follow the project specifications exactly.
